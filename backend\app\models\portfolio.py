"""
Portfolio and strategy models for automated trading
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, Enum, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, List, Optional
import enum

from app.core.database import Base

class StrategyStatus(enum.Enum):
    """Strategy status enumeration"""
    ACTIVE = "active"
    PAUSED = "paused"
    STOPPED = "stopped"
    BACKTESTING = "backtesting"

class ScreeningCriteria(enum.Enum):
    """Screening criteria types"""
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    CUSTOM = "custom"

class Portfolio(Base):
    """Portfolio model for grouping strategies and tracking performance"""
    
    __tablename__ = "portfolios"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key
    user_id = Column(Integer, nullable=False, index=True)
    
    # Portfolio information
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Portfolio settings
    initial_capital = Column(Float, nullable=False, default=10000.0)
    current_capital = Column(Float, nullable=False, default=10000.0)
    available_cash = Column(Float, nullable=False, default=10000.0)
    
    # Risk management
    max_position_size = Column(Float, default=0.10)  # 10% max per position
    max_daily_loss = Column(Float, default=0.05)  # 5% max daily loss
    max_total_risk = Column(Float, default=0.20)  # 20% max total portfolio risk
    
    # Performance metrics
    total_return = Column(Float, default=0.0)
    total_return_percent = Column(Float, default=0.0)
    day_return = Column(Float, default=0.0)
    day_return_percent = Column(Float, default=0.0)
    
    # Risk metrics
    sharpe_ratio = Column(Float)
    max_drawdown = Column(Float)
    volatility = Column(Float)
    beta = Column(Float)
    
    # Trading statistics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_paper_trading = Column(Boolean, default=True)

    # Rebalancing settings
    auto_rebalance = Column(Boolean, default=False)
    rebalance_frequency = Column(String(20))  # daily, weekly, monthly, quarterly
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_rebalanced = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="portfolios")
    strategies = relationship("Strategy", back_populates="portfolio", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Portfolio(id={self.id}, name='{self.name}', capital={self.current_capital})>"
    
    @property
    def market_value(self) -> float:
        """Calculate total market value of portfolio"""
        # This would be calculated from positions
        return self.current_capital
    
    @property
    def invested_amount(self) -> float:
        """Calculate amount currently invested"""
        return self.current_capital - self.available_cash
    
    @property
    def cash_percentage(self) -> float:
        """Calculate cash percentage"""
        if self.current_capital == 0:
            return 0.0
        return (self.available_cash / self.current_capital) * 100
    
    @property
    def is_profitable(self) -> bool:
        """Check if portfolio is profitable"""
        return self.total_return > 0
    
    def update_performance_metrics(self):
        """Update portfolio performance metrics"""
        if self.initial_capital > 0:
            self.total_return = self.current_capital - self.initial_capital
            self.total_return_percent = (self.total_return / self.initial_capital) * 100
        
        # Calculate win rate
        if self.total_trades > 0:
            self.win_rate = (self.winning_trades / self.total_trades) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert portfolio to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "initial_capital": self.initial_capital,
            "current_capital": self.current_capital,
            "available_cash": self.available_cash,
            "market_value": self.market_value,
            "invested_amount": self.invested_amount,
            "cash_percentage": self.cash_percentage,
            "max_position_size": self.max_position_size,
            "max_daily_loss": self.max_daily_loss,
            "max_total_risk": self.max_total_risk,
            "total_return": self.total_return,
            "total_return_percent": self.total_return_percent,
            "day_return": self.day_return,
            "day_return_percent": self.day_return_percent,
            "sharpe_ratio": self.sharpe_ratio,
            "max_drawdown": self.max_drawdown,
            "volatility": self.volatility,
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": self.win_rate,
            "is_active": self.is_active,
            "is_paper_trading": self.is_paper_trading,
            "is_profitable": self.is_profitable,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_rebalanced": self.last_rebalanced.isoformat() if self.last_rebalanced else None,
        }

class Strategy(Base):
    """Trading strategy model"""
    
    __tablename__ = "strategies"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign keys
    user_id = Column(Integer, nullable=False, index=True)
    portfolio_id = Column(Integer, nullable=False, index=True)
    
    # Strategy information
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Strategy configuration
    screening_criteria = Column(JSON, nullable=False)  # Screening rules
    entry_rules = Column(JSON, nullable=False)  # Entry conditions
    exit_rules = Column(JSON, nullable=False)  # Exit conditions
    risk_rules = Column(JSON)  # Risk management rules
    
    # Position sizing
    position_size_method = Column(String(20), default="fixed")  # fixed, percent, kelly
    position_size_value = Column(Float, default=0.02)  # 2% default
    max_positions = Column(Integer, default=10)
    
    # Execution settings
    order_type = Column(String(20), default="market")  # market, limit
    execution_delay = Column(Integer, default=0)  # seconds
    
    # Status and control
    status = Column(Enum(StrategyStatus), default=StrategyStatus.PAUSED)
    is_paper_trading = Column(Boolean, default=True)
    
    # Performance tracking
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    total_return = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    
    # Execution tracking
    last_scan_time = Column(DateTime(timezone=True))
    next_scan_time = Column(DateTime(timezone=True))
    scan_interval = Column(Integer, default=300)  # seconds
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="strategies")
    portfolio = relationship("Portfolio", back_populates="strategies")
    trades = relationship("Trade", back_populates="strategy")
    screening_results = relationship("ScreeningResult", back_populates="strategy")
    
    # Indexes
    __table_args__ = (
        Index('idx_strategy_status', 'status'),
        Index('idx_strategy_next_scan', 'next_scan_time'),
    )
    
    def __repr__(self):
        return f"<Strategy(id={self.id}, name='{self.name}', status='{self.status.value}')>"
    
    @property
    def is_active(self) -> bool:
        """Check if strategy is active"""
        return self.status == StrategyStatus.ACTIVE
    
    @property
    def win_rate(self) -> float:
        """Calculate win rate"""
        if self.total_trades == 0:
            return 0.0
        return (self.winning_trades / self.total_trades) * 100
    
    @property
    def is_due_for_scan(self) -> bool:
        """Check if strategy is due for scanning"""
        if not self.next_scan_time:
            return True
        return datetime.utcnow() >= self.next_scan_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert strategy to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "screening_criteria": self.screening_criteria,
            "entry_rules": self.entry_rules,
            "exit_rules": self.exit_rules,
            "risk_rules": self.risk_rules,
            "position_size_method": self.position_size_method,
            "position_size_value": self.position_size_value,
            "max_positions": self.max_positions,
            "order_type": self.order_type,
            "execution_delay": self.execution_delay,
            "status": self.status.value,
            "is_paper_trading": self.is_paper_trading,
            "is_active": self.is_active,
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "win_rate": self.win_rate,
            "total_return": self.total_return,
            "max_drawdown": self.max_drawdown,
            "scan_interval": self.scan_interval,
            "is_due_for_scan": self.is_due_for_scan,
            "last_scan_time": self.last_scan_time.isoformat() if self.last_scan_time else None,
            "next_scan_time": self.next_scan_time.isoformat() if self.next_scan_time else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


